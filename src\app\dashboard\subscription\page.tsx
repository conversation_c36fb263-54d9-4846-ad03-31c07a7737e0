'use client'

import React, { useState, useEffect } from 'react'
import { 
  Package, 
  Calendar, 
  TrendingUp, 
  FileText,
  Crown,
  Zap,
  Clock,
  CheckCircle
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { useAuth } from '@/contexts/AuthContext'
import { SubscriptionService } from '@/lib/services/subscriptions'
import { WalletService } from '@/lib/services/wallet'
import { SubscriptionPackage, UserSubscription, SubscriptionUsage } from '@/types'
import { showConfirmation, showAlert } from '@/components/ui/ConfirmationDialog'

export default function SubscriptionPage() {
  const { user } = useAuth()
  const [packages, setPackages] = useState<SubscriptionPackage[]>([])
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null)
  const [usage, setUsage] = useState<SubscriptionUsage | null>(null)
  const [walletBalance, setWalletBalance] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      loadData()
    }
  }, [user])

  const loadData = async () => {
    if (!user) return

    try {
      setLoading(true)
      const [packagesData, subscriptionData, usageData, walletData] = await Promise.all([
        SubscriptionService.getPackages().catch(err => {
          console.error('Error loading packages:', err)
          return []
        }),
        SubscriptionService.getUserActiveSubscription(user.id).catch(err => {
          console.error('Error loading subscription:', err)
          return null
        }),
        SubscriptionService.getUserSubscriptionUsage(user.id).catch(err => {
          console.error('Error loading usage:', err)
          return {
            ads_remaining: 0,
            boosts_remaining: 0,
            ads_used: 0,
            boosts_used: 0,
            subscription_expires_at: null,
            has_active_subscription: false
          }
        }),
        WalletService.getUserWallet(user.id).catch(err => {
          console.error('Error loading wallet:', err)
          return null
        })
      ])

      setPackages(packagesData)
      setCurrentSubscription(subscriptionData)
      setUsage(usageData)
      setWalletBalance(walletData?.balance || 0)
    } catch (error) {
      console.error('Error loading subscription data:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to load subscription data. Please refresh the page.',
        variant: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePurchaseSubscription = async (packageData: SubscriptionPackage) => {
    if (!user) return

    if (walletBalance < packageData.price) {
      await showAlert({
        title: 'Insufficient Balance',
        message: `You need Rs ${packageData.price.toLocaleString()} to purchase this package. Your current balance is Rs ${walletBalance.toLocaleString()}. Please add funds to your wallet.`,
        variant: 'warning'
      })
      return
    }

    const confirmed = await showConfirmation({
      title: 'Purchase Subscription',
      message: `Are you sure you want to purchase ${packageData.name} for Rs ${packageData.price.toLocaleString()}? This will be deducted from your wallet balance.`,
      confirmText: 'Purchase',
      cancelText: 'Cancel',
      variant: 'warning'
    })

    if (!confirmed) return

    setPurchasing(packageData.id)

    try {
      const walletData = await WalletService.getUserWallet(user.id)
      if (!walletData) {
        throw new Error('Wallet not found')
      }

      await SubscriptionService.purchaseSubscription(user.id, packageData.id, walletData.id)
      
      await showAlert({
        title: 'Success',
        message: `Successfully purchased ${packageData.name}! Your subscription is now active.`,
        variant: 'success'
      })

      await loadData() // Refresh data
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to purchase subscription. Please try again.',
        variant: 'danger'
      })
    } finally {
      setPurchasing(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getProgressPercentage = (used: number, total: number) => {
    return Math.min((used / total) * 100, 100)
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription</h1>
          <p className="text-gray-600">Manage your subscription and view usage statistics</p>
        </div>

        {/* Current Subscription Status */}
        {usage?.has_active_subscription ? (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Crown className="h-6 w-6 text-blue-600 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">Active Subscription</h2>
              </div>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                Active
              </span>
            </div>
            
            {currentSubscription && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-600">Package</p>
                  <p className="font-semibold text-gray-900">{(currentSubscription.package as SubscriptionPackage)?.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Expires</p>
                  <p className="font-semibold text-gray-900">{formatDate(currentSubscription.expires_at)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Wallet Balance</p>
                  <p className="font-semibold text-gray-900">Rs {walletBalance.toLocaleString()}</p>
                </div>
              </div>
            )}

            {/* Usage Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Ads Posted</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    {usage.ads_used} / {usage.ads_used + usage.ads_remaining}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage(usage.ads_used, usage.ads_used + usage.ads_remaining)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{usage.ads_remaining} remaining</p>
              </div>

              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <Zap className="h-4 w-4 text-orange-600 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Boosts Used</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    {usage.boosts_used} / {usage.boosts_used + usage.boosts_remaining}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage(usage.boosts_used, usage.boosts_used + usage.boosts_remaining)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{usage.boosts_remaining} remaining</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gray-50 rounded-xl p-6 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">No Active Subscription</h2>
            <p className="text-gray-600 mb-4">
              Purchase a subscription package to start posting ads and boost your listings.
            </p>
            <p className="text-sm text-gray-500">
              Wallet Balance: Rs {walletBalance.toLocaleString()}
            </p>
          </div>
        )}

        {/* Available Packages */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Packages</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {packages.map((pkg) => (
              <div key={pkg.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{pkg.name}</h3>
                  <div className="text-3xl font-bold text-primary-blue mb-1">
                    Rs {pkg.price.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-500">{pkg.duration_days} days</p>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm text-gray-700">{pkg.ad_limit} ads</span>
                  </div>
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 text-orange-600 mr-2" />
                    <span className="text-sm text-gray-700">{pkg.boost_limit} boosts</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-sm text-gray-700">{pkg.duration_days} days validity</span>
                  </div>
                </div>

                <button
                  onClick={() => handlePurchaseSubscription(pkg)}
                  disabled={purchasing === pkg.id || walletBalance < pkg.price}
                  className="w-full px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {purchasing === pkg.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Purchasing...
                    </div>
                  ) : walletBalance < pkg.price ? (
                    'Insufficient Balance'
                  ) : (
                    'Purchase'
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
