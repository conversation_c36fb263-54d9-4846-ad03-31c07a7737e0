'use client'

import React, { useState, useEffect } from 'react'
import {
  Wallet,
  Plus,
  Send,
  Download,
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  CheckCircle,
  XCircle,
  Filter,
  Search,
  CreditCard,
  Banknote,
  TrendingUp,
  Users,
  Crown,
  Network,
  Target
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import CommissionBreakdownCards from '@/components/wallet/CommissionBreakdownCards'
import { useAuth } from '@/contexts/AuthContext'
import { WalletService } from '@/lib/services/wallet'
import { StorageService } from '@/lib/services/storage'
import { CommissionSystemService, CommissionBreakdown } from '@/lib/services/commissionSystem'
import { UserWallet, WalletTransaction, P2PTransfer, DepositRequest, WithdrawalRequest } from '@/types'
import { formatCurrency } from '@/lib/utils'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { useAlert } from '@/contexts/AlertContext'

interface WalletStats {
  totalBalance: number
  totalDeposits: number
  transfersSent: number
  transfersReceived: number
  totalWithdrawals: number
  pendingDeposits: number
}

export default function WalletPage() {
  const { user } = useAuth()
  const { showAlert } = useAlert()
  const [wallet, setWallet] = useState<UserWallet | null>(null)
  const [transactions, setTransactions] = useState<WalletTransaction[]>([])
  const [transfers, setTransfers] = useState<P2PTransfer[]>([])
  const [depositRequests, setDepositRequests] = useState<DepositRequest[]>([])
  const [withdrawalRequests, setWithdrawalRequests] = useState<WithdrawalRequest[]>([])
  const [stats, setStats] = useState<WalletStats>({
    totalBalance: 0,
    totalDeposits: 0,
    transfersSent: 0,
    transfersReceived: 0,
    totalWithdrawals: 0,
    pendingDeposits: 0
  })
  const [commissionBreakdown, setCommissionBreakdown] = useState<CommissionBreakdown>({
    directCommission: 0,
    levelCommission: 0,
    rsmBonus: 0,
    zmBonus: 0,
    okdoiHeadCommission: 0,
    totalCommissions: 0
  })
  const [extendedCommissionData, setExtendedCommissionData] = useState({
    directCommission: 0,
    levelCommission: 0,
    voucherCommission: 0,
    festivalBonus: 0,
    savingCommission: 0,
    giftCenterCommission: 0,
    entertainmentCommission: 0,
    medicalCommission: 0,
    educationCommission: 0,
    creditCommission: 0,
    // ZM specific
    zmBonuses: 0,
    petralAllowanceZM: 0,
    leasingFacilityZM: 0,
    phoneBillZM: 0,
    // RSM specific
    rsmBonuses: 0,
    petralAllowanceRSM: 0,
    leasingFacilityRSM: 0,
    phoneBillRSM: 0,
    // OKDOI Head specific
    okdoiHeadCommission: 0,
    totalCommissions: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'all_transactions' | 'fund_transfers' | 'deposits' | 'withdrawals' | 'commissions'>('all_transactions')

  // Modal states
  const [showTransferModal, setShowTransferModal] = useState(false)
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false)

  // Transfer form
  const [transferForm, setTransferForm] = useState({
    receiverEmail: '',
    amount: '',
    description: ''
  })
  const [transferLoading, setTransferLoading] = useState(false)

  // Deposit form
  const [depositForm, setDepositForm] = useState({
    amount: '',
    depositor_name: '',
    notes: '',
    terms_accepted: false
  })
  const [depositProof, setDepositProof] = useState<File | null>(null)
  const [depositLoading, setDepositLoading] = useState(false)

  // Withdrawal form
  const [withdrawalForm, setWithdrawalForm] = useState({
    amount: '',
    bank_name: '',
    account_number: '',
    account_holder_name: user?.full_name || '',
    branch: '',
    notes: ''
  })
  const [withdrawalLoading, setWithdrawalLoading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchWalletData()
    }
  }, [user])

  const fetchWalletData = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      console.log('Fetching wallet data for user:', user.id)

      // Fetch wallet with error handling
      const walletData = await WalletService.getUserWallet(user.id)
      setWallet(walletData)
      console.log('Wallet data fetched:', walletData)

      // Fetch recent transactions with error handling
      const { transactions: transactionsData } = await WalletService.getWalletTransactions(user.id, 1, 10)
      setTransactions(transactionsData || [])
      console.log('Transactions fetched:', transactionsData?.length || 0)

      // Fetch recent transfers with error handling
      const { transfers: transfersData } = await WalletService.getUserTransfers(user.id, 1, 10)
      setTransfers(transfersData || [])
      console.log('Transfers fetched:', transfersData?.length || 0)

      // Fetch deposit requests with error handling
      const { requests: depositsData } = await WalletService.getUserDepositRequests(user.id, 1, 10)
      setDepositRequests(depositsData || [])
      console.log('Deposit requests fetched:', depositsData?.length || 0)

      // Fetch withdrawal requests with error handling
      const { requests: withdrawalsData } = await WalletService.getUserWithdrawalRequests(user.id, 1, 10)
      setWithdrawalRequests(withdrawalsData || [])
      console.log('Withdrawal requests fetched:', withdrawalsData?.length || 0)

      // Fetch commission breakdown
      try {
        const commissionData = await CommissionSystemService.getCommissionBreakdown(user.id)
        setCommissionBreakdown(commissionData)

        // Get real extended commission data from the database
        const extendedData = await CommissionSystemService.getExtendedCommissionBreakdown(user.id)
        setExtendedCommissionData(extendedData)

        console.log('Commission breakdown fetched:', commissionData)
      } catch (commissionError) {
        console.error('Error fetching commission breakdown:', commissionError)
        // Don't fail the entire load if commission data fails
      }

      // Calculate stats with null safety
      const totalDeposits = (transactionsData || [])
        .filter(t => t.transaction_type === 'deposit' && t.status === 'completed')
        .reduce((sum, t) => sum + (t.amount || 0), 0)

      const transfersSent = (transfersData || [])
        .filter(t => t.sender_id === user.id && t.status === 'completed')
        .reduce((sum, t) => sum + (t.amount || 0), 0)

      const transfersReceived = (transfersData || [])
        .filter(t => t.receiver_id === user.id && t.status === 'completed')
        .reduce((sum, t) => sum + (t.amount || 0), 0)

      const totalWithdrawals = (transactionsData || [])
        .filter(t => t.transaction_type === 'withdrawal' && t.status === 'completed')
        .reduce((sum, t) => sum + (t.amount || 0), 0)

      const pendingDeposits = (depositsData || [])
        .filter(d => d.status === 'pending')
        .reduce((sum, d) => sum + (d.amount || 0), 0)

      setStats({
        totalBalance: walletData?.balance || 0,
        totalDeposits,
        transfersSent,
        transfersReceived,
        totalWithdrawals,
        pendingDeposits
      })

      console.log('Wallet data fetch completed successfully')

    } catch (err) {
      console.error('Error fetching wallet data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load wallet data')

      // Reset data on error
      setWallet(null)
      setTransactions([])
      setTransfers([])
      setDepositRequests([])
      setWithdrawalRequests([])
      setStats({
        totalBalance: 0,
        totalDeposits: 0,
        transfersSent: 0,
        transfersReceived: 0,
        totalWithdrawals: 0,
        pendingDeposits: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      setTransferLoading(true)
      await WalletService.createP2PTransfer(
        user.id,
        transferForm.receiverEmail,
        parseFloat(transferForm.amount),
        transferForm.description || undefined
      )

      setShowTransferModal(false)
      setTransferForm({ receiverEmail: '', amount: '', description: '' })
      await fetchWalletData()

      await showAlert({
        title: 'Success',
        message: 'Transfer completed successfully!',
        variant: 'success'
      })
    } catch (err) {
      await showAlert({
        title: 'Error',
        message: err instanceof Error ? err.message : 'Transfer failed',
        variant: 'danger'
      })
    } finally {
      setTransferLoading(false)
    }
  }

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Validation
    if (!depositForm.terms_accepted) {
      await showAlert({
        title: 'Terms Required',
        message: 'Please accept the terms and conditions to proceed.',
        variant: 'warning'
      })
      return
    }

    if (!depositProof) {
      await showAlert({
        title: 'Proof Required',
        message: 'Please upload a deposit receipt or proof of payment.',
        variant: 'warning'
      })
      return
    }

    try {
      setDepositLoading(true)

      // Upload proof file first
      let proofUrl = ''
      if (depositProof) {
        proofUrl = await StorageService.uploadImage(depositProof, user.id)
      }

      await WalletService.createDepositRequest(user.id, {
        amount: parseFloat(depositForm.amount),
        depositor_name: depositForm.depositor_name,
        notes: depositForm.notes || undefined,
        deposit_slip_url: proofUrl
      })

      setShowDepositModal(false)
      setDepositForm({
        amount: '',
        depositor_name: '',
        notes: '',
        terms_accepted: false
      })
      setDepositProof(null)
      await fetchWalletData()

      await showAlert({
        title: 'Success',
        message: 'Deposit request submitted successfully! We will review it shortly.',
        variant: 'success'
      })
    } catch (err) {
      await showAlert({
        title: 'Error',
        message: err instanceof Error ? err.message : 'Failed to submit deposit request',
        variant: 'danger'
      })
    } finally {
      setDepositLoading(false)
    }
  }

  const handleWithdrawal = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      setWithdrawalLoading(true)
      await WalletService.createWithdrawalRequest(user.id, {
        amount: parseFloat(withdrawalForm.amount),
        bank_name: withdrawalForm.bank_name,
        account_number: withdrawalForm.account_number,
        account_holder_name: withdrawalForm.account_holder_name,
        branch: withdrawalForm.branch || undefined,
        notes: withdrawalForm.notes || undefined
      })

      setShowWithdrawalModal(false)
      setWithdrawalForm({
        amount: '',
        bank_name: '',
        account_number: '',
        account_holder_name: user?.full_name || '',
        branch: '',
        notes: ''
      })
      await fetchWalletData()

      await showAlert({
        title: 'Success',
        message: 'Withdrawal request submitted successfully! We will process it shortly.',
        variant: 'success'
      })
    } catch (err) {
      await showAlert({
        title: 'Error',
        message: err instanceof Error ? err.message : 'Failed to submit withdrawal request',
        variant: 'danger'
      })
    } finally {
      setWithdrawalLoading(false)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
      case 'transfer_in':
      case 'refund':
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />
      case 'withdrawal':
      case 'transfer_out':
      case 'purchase':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />
      case 'commission':
        return <Banknote className="h-4 w-4 text-green-600" />
      default:
        return <Wallet className="h-4 w-4 text-gray-600" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit':
      case 'transfer_in':
      case 'refund':
      case 'commission':
        return 'text-green-600'
      case 'withdrawal':
      case 'transfer_out':
      case 'purchase':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'failed':
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner />
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-red-600 text-center">
            <h3 className="text-lg font-semibold mb-2">Error Loading Wallet</h3>
            <p className="text-sm">{error}</p>
          </div>
          <button
            onClick={fetchWalletData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </DashboardLayout>
    )
  }

  if (!wallet) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Wallet Not Found</h3>
            <p className="text-sm text-gray-600">Your wallet is being set up. Please try again in a moment.</p>
          </div>
          <button
            onClick={fetchWalletData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Refresh
          </button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Wallet</h1>
            <p className="text-gray-600">Manage your funds and transactions</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setShowDepositModal(true)}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Funds
            </button>
            <button
              onClick={() => setShowTransferModal(true)}
              className="flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
            >
              <Send className="h-4 w-4 mr-2" />
              Send Money
            </button>
            <button
              onClick={() => setShowWithdrawalModal(true)}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Withdraw
            </button>
          </div>
        </div>

        {/* Wallet Balance Card */}
        <div className="bg-gradient-to-r from-primary-blue to-secondary-blue rounded-xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 mb-2">Available Balance</p>
              <h2 className="text-4xl font-bold">{formatCurrency(wallet?.balance || 0)}</h2>
            </div>
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <Wallet className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <ArrowDownLeft className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Deposits</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalDeposits)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <ArrowUpRight className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Transfers Sent</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.transfersSent)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ArrowDownLeft className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Transfers Received</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.transfersReceived)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Download className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Withdrawals</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalWithdrawals)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Deposits</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.pendingDeposits)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Commission Breakdown */}
        <CommissionBreakdownCards
          data={extendedCommissionData}
          userType={user.user_type}
        />

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all_transactions', name: 'All Transactions', icon: CreditCard },
              { id: 'fund_transfers', name: 'Fund Transfers', icon: Send },
              { id: 'deposits', name: 'Deposits', icon: Plus },
              { id: 'withdrawals', name: 'Withdrawals', icon: Download },
              { id: 'commissions', name: 'Commissions', icon: Banknote }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-blue text-primary-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">

          {activeTab === 'all_transactions' && (
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">All Transactions</h3>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <Filter className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <Search className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center">
                      {getTransactionIcon(transaction.transaction_type)}
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900 capitalize">
                          {transaction.transaction_type.replace('_', ' ')}
                        </p>
                        <p className="text-sm text-gray-500">
                          {transaction.reference_type === 'p2p_transfer' && transaction.p2p_transfer ? (
                            transaction.transaction_type === 'transfer_out' ? (
                              `Transfer to ${transaction.p2p_transfer.receiver?.full_name || 'Unknown'}`
                            ) : (
                              `Transfer from ${transaction.p2p_transfer.sender?.full_name || 'Unknown'}`
                            )
                          ) : (
                            transaction.description
                          )}
                        </p>
                        {transaction.reference_number && (
                          <p className="text-xs text-gray-400 font-mono">
                            Ref: {transaction.reference_number}
                          </p>
                        )}
                        <p className="text-xs text-gray-400">
                          {new Date(transaction.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center">
                        <p className={`text-sm font-medium mr-2 ${getTransactionColor(transaction.transaction_type)}`}>
                          {transaction.transaction_type.includes('in') || transaction.transaction_type === 'deposit' || transaction.transaction_type === 'refund' ? '+' : '-'}
                          {formatCurrency(transaction.amount)}
                        </p>
                        {getStatusIcon(transaction.status)}
                      </div>
                      <p className="text-xs text-gray-500">
                        Balance: {formatCurrency(transaction.balance_after)}
                      </p>
                    </div>
                  </div>
                ))}
                {transactions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No transactions found
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'fund_transfers' && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">P2P Transfers</h3>
              <div className="space-y-4">
                {transfers.map((transfer) => (
                  <div key={transfer.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        {transfer.sender_id === user?.id ? (
                          <ArrowUpRight className="h-5 w-5 text-blue-600" />
                        ) : (
                          <ArrowDownLeft className="h-5 w-5 text-green-600" />
                        )}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {transfer.sender_id === user?.id ? 'Sent to' : 'Received from'}{' '}
                          {transfer.sender_id === user?.id ? transfer.receiver?.full_name : transfer.sender?.full_name}
                        </p>
                        <p className="text-sm text-gray-500">{transfer.description}</p>
                        {transfer.reference_number && (
                          <p className="text-xs text-gray-400 font-mono">
                            Ref: {transfer.reference_number}
                          </p>
                        )}
                        <p className="text-xs text-gray-400">
                          {new Date(transfer.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center">
                        <p className={`text-sm font-medium mr-2 ${
                          transfer.sender_id === user?.id ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transfer.sender_id === user?.id ? '-' : '+'}
                          {formatCurrency(transfer.amount)}
                        </p>
                        {getStatusIcon(transfer.status)}
                      </div>
                    </div>
                  </div>
                ))}
                {transfers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No transfers found
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'deposits' && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Deposit Requests</h3>
              <div className="space-y-4">
                {depositRequests.map((deposit) => (
                  <div key={deposit.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <Banknote className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">
                            Bank Deposit - {deposit.bank_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {deposit.account_holder_name} ({deposit.account_number})
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-gray-900">
                          {formatCurrency(deposit.amount)}
                        </p>
                        <div className="flex items-center">
                          {getStatusIcon(deposit.status)}
                          <span className={`ml-1 text-xs font-medium capitalize ${
                            deposit.status === 'approved' ? 'text-green-600' :
                            deposit.status === 'rejected' ? 'text-red-600' : 'text-yellow-600'
                          }`}>
                            {deposit.status}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 space-y-1">
                      <p>Submitted: {new Date(deposit.created_at).toLocaleString()}</p>
                      {deposit.reference_number && (
                        <p className="font-mono">Ref: {deposit.reference_number}</p>
                      )}
                      {deposit.transaction_reference && (
                        <p>Bank Reference: {deposit.transaction_reference}</p>
                      )}
                      {deposit.notes && (
                        <p>Notes: {deposit.notes}</p>
                      )}
                      {deposit.admin_notes && (
                        <p>Admin Notes: {deposit.admin_notes}</p>
                      )}
                    </div>
                  </div>
                ))}
                {depositRequests.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No deposit requests found
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'withdrawals' && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Withdrawal Requests</h3>
              <div className="space-y-4">
                {withdrawalRequests.map((withdrawal) => (
                  <div key={withdrawal.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">
                            {formatCurrency(withdrawal.amount)} to {withdrawal.bank_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            Account: {withdrawal.account_number} ({withdrawal.account_holder_name})
                          </p>
                          {withdrawal.branch && (
                            <p className="text-sm text-gray-500">Branch: {withdrawal.branch}</p>
                          )}
                          {withdrawal.reference_number && (
                            <p className="text-xs text-gray-400 font-mono">
                              Ref: {withdrawal.reference_number}
                            </p>
                          )}
                          {withdrawal.notes && (
                            <p className="text-sm text-gray-500">Notes: {withdrawal.notes}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            withdrawal.status === 'processed' ? 'bg-green-100 text-green-800' :
                            withdrawal.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                            withdrawal.status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {withdrawal.status}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 space-y-1 ml-4">
                      <p>Submitted: {new Date(withdrawal.created_at).toLocaleString()}</p>
                      {withdrawal.approved_at && (
                        <p>Approved: {new Date(withdrawal.approved_at).toLocaleString()}</p>
                      )}
                      {withdrawal.processed_at && (
                        <p>Processed: {new Date(withdrawal.processed_at).toLocaleString()}</p>
                      )}
                      {withdrawal.admin_notes && (
                        <p>Admin Notes: {withdrawal.admin_notes}</p>
                      )}
                    </div>
                  </div>
                ))}
                {withdrawalRequests.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No withdrawal requests found
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'commissions' && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Commission Transactions</h3>
              <div className="space-y-4">
                {transactions.filter(t => t.transaction_type === 'commission').map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center">
                      {getTransactionIcon(transaction.transaction_type)}
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          Commission Earned
                        </p>
                        <p className="text-sm text-gray-500">
                          {transaction.description}
                        </p>
                        {transaction.reference_id && (
                          <p className="text-xs text-gray-400 font-mono">
                            ID: {transaction.reference_id.slice(-8)}
                          </p>
                        )}
                        <p className="text-xs text-gray-400">
                          {new Date(transaction.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center">
                        <p className="text-sm font-medium mr-2 text-green-600">
                          +{formatCurrency(transaction.amount)}
                        </p>
                        {getStatusIcon(transaction.status)}
                      </div>
                      <p className="text-xs text-gray-500">
                        Balance: {formatCurrency(transaction.balance_after)}
                      </p>
                    </div>
                  </div>
                ))}
                {transactions.filter(t => t.transaction_type === 'commission').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Banknote className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p>No commission transactions found</p>
                    <p className="text-sm mt-2">Commissions will appear here when you earn them from referrals or other activities.</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Transfer Modal */}
      {showTransferModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Money</h3>
            <form onSubmit={handleTransfer} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Receiver Email
                </label>
                <input
                  type="email"
                  required
                  value={transferForm.receiverEmail}
                  onChange={(e) => setTransferForm(prev => ({ ...prev, receiverEmail: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Enter receiver's email"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (Rs)
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  max={wallet?.balance || 0}
                  step="0.01"
                  value={transferForm.amount}
                  onChange={(e) => setTransferForm(prev => ({ ...prev, amount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Available: {formatCurrency(wallet?.balance || 0)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (Optional)
                </label>
                <input
                  type="text"
                  value={transferForm.description}
                  onChange={(e) => setTransferForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="What's this for?"
                />
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowTransferModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={transferLoading}
                  className="flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50"
                >
                  {transferLoading ? 'Sending...' : 'Send Money'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Deposit Modal */}
      {showDepositModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Cash Deposit</h3>
            <form onSubmit={handleDeposit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (Rs) *
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  step="0.01"
                  value={depositForm.amount}
                  onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Depositor Name *
                </label>
                <input
                  type="text"
                  required
                  value={depositForm.depositor_name}
                  onChange={(e) => setDepositForm(prev => ({ ...prev, depositor_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Name of person who made the deposit"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Deposit Receipt/Proof *
                </label>
                <input
                  type="file"
                  required
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      // Validate file size (5MB max)
                      if (file.size > 5 * 1024 * 1024) {
                        showAlert({
                          title: 'File Too Large',
                          message: 'Please select a file smaller than 5MB.',
                          variant: 'warning'
                        })
                        e.target.value = ''
                        return
                      }
                      setDepositProof(file)
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload bank deposit slip, receipt, or proof of payment (Max 5MB)
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (Optional)
                </label>
                <textarea
                  value={depositForm.notes}
                  onChange={(e) => setDepositForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  rows={3}
                  placeholder="Additional information about the deposit"
                />
              </div>
              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id="terms"
                  required
                  checked={depositForm.terms_accepted}
                  onChange={(e) => setDepositForm(prev => ({ ...prev, terms_accepted: e.target.checked }))}
                  className="mt-1 h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                />
                <label htmlFor="terms" className="text-sm text-gray-700">
                  I agree to the{' '}
                  <a href="/terms" target="_blank" className="text-primary-blue hover:underline">
                    Terms and Conditions
                  </a>{' '}
                  and confirm that the deposit information provided is accurate. *
                </label>
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowDepositModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={depositLoading}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {depositLoading ? 'Submitting...' : 'Submit Request'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Withdrawal Modal */}
      {showWithdrawalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Withdrawal</h3>
            <form onSubmit={handleWithdrawal} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (Rs)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="1"
                  max={wallet?.balance || 0}
                  required
                  value={withdrawalForm.amount}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter amount"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Available balance: {formatCurrency(wallet?.balance || 0)}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bank Name
                </label>
                <input
                  type="text"
                  required
                  value={withdrawalForm.bank_name}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, bank_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter bank name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Account Number
                </label>
                <input
                  type="text"
                  required
                  value={withdrawalForm.account_number}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, account_number: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter account number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Account Holder Name
                </label>
                <input
                  type="text"
                  required
                  value={withdrawalForm.account_holder_name}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, account_holder_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter account holder name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Branch (Optional)
                </label>
                <input
                  type="text"
                  value={withdrawalForm.branch}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, branch: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter branch name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (Optional)
                </label>
                <textarea
                  value={withdrawalForm.notes}
                  onChange={(e) => setWithdrawalForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  rows={3}
                  placeholder="Any additional notes..."
                />
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowWithdrawalModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={withdrawalLoading}
                  className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  {withdrawalLoading ? 'Submitting...' : 'Submit Request'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </DashboardLayout>
  )
}
