'use client'

import React, { useState } from 'react'
import { MessageCircle, Search, User, Clock, Eye } from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import ChatModal from '@/components/ChatModal'
import { useConversations } from '@/hooks/useChats'
import { ChatConversation, Ad } from '@/types'
import { useAuth } from '@/contexts/AuthContext'
import { formatDistanceToNow } from 'date-fns'

export default function ChatsPage() {
  const { user } = useAuth()
  const { data: conversations = [], isLoading: loading, refetch } = useConversations()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null)
  const [isChatModalOpen, setIsChatModalOpen] = useState(false)



  const filteredConversations = conversations.filter(conversation => {
    if (!searchTerm) return true
    
    const searchLower = searchTerm.toLowerCase()
    const adTitle = conversation.ad?.title?.toLowerCase() || ''
    const otherUserName = (conversation.buyer_id === user?.id 
      ? conversation.seller?.full_name 
      : conversation.buyer?.full_name)?.toLowerCase() || ''
    
    return adTitle.includes(searchLower) || otherUserName.includes(searchLower)
  })

  const openChat = (conversation: ChatConversation) => {
    if (conversation.ad) {
      setSelectedConversation(conversation)
      setIsChatModalOpen(true)
    }
  }

  const getUnreadCount = (conversation: ChatConversation) => {
    return user?.id === conversation.buyer_id 
      ? conversation.buyer_unread_count 
      : conversation.seller_unread_count
  }

  const getOtherUser = (conversation: ChatConversation) => {
    return user?.id === conversation.buyer_id 
      ? conversation.seller 
      : conversation.buyer
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Chats</h1>
            <p className="text-gray-600">Manage your conversations with buyers and sellers</p>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
          />
        </div>

        {/* Conversations List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="text-center py-12">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'No conversations found' : 'No conversations yet'}
              </h3>
              <p className="text-gray-500">
                {searchTerm 
                  ? 'Try adjusting your search terms' 
                  : 'Start chatting with buyers and sellers to see conversations here'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredConversations.map((conversation) => {
                const otherUser = getOtherUser(conversation)
                const unreadCount = getUnreadCount(conversation)
                const isFromCurrentUser = conversation.last_message?.sender_id === user?.id

                return (
                  <div
                    key={conversation.id}
                    onClick={() => openChat(conversation)}
                    className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-start space-x-4">
                      {/* Ad Image */}
                      <div className="flex-shrink-0">
                        <img
                          src={conversation.ad?.images?.[0] || '/placeholder-image.jpg'}
                          alt={conversation.ad?.title}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                      </div>

                      {/* Conversation Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-sm font-semibold text-gray-900 truncate">
                              {conversation.ad?.title}
                            </h3>
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              <User className="h-4 w-4 mr-1" />
                              <span>{otherUser?.full_name || 'Unknown User'}</span>
                              <span className="mx-2">•</span>
                              <span className="text-primary-blue font-medium">
                                Rs {conversation.ad?.price?.toLocaleString()}
                              </span>
                            </div>
                            {conversation.last_message && (
                              <p className="text-sm text-gray-600 mt-2 truncate">
                                {isFromCurrentUser && (
                                  <span className="text-gray-400">You: </span>
                                )}
                                {conversation.last_message.message}
                              </p>
                            )}
                          </div>
                          
                          <div className="flex flex-col items-end space-y-1">
                            {conversation.last_message_at && (
                              <span className="text-xs text-gray-400">
                                {formatDistanceToNow(new Date(conversation.last_message_at), { addSuffix: true })}
                              </span>
                            )}
                            {unreadCount > 0 && (
                              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
                                {unreadCount}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Chat Modal */}
      {selectedConversation && selectedConversation.ad && (
        <ChatModal
          isOpen={isChatModalOpen}
          onClose={() => {
            setIsChatModalOpen(false)
            setSelectedConversation(null)
            // Refresh conversations to update unread counts
            refetch()
          }}
          ad={selectedConversation.ad as any}
          existingConversation={selectedConversation}
        />
      )}
    </DashboardLayout>
  )
}
