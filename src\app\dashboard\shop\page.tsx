'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  Store,
  Plus,
  Edit,
  Trash2,
  Eye,
  Package,
  Star,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Camera,
  Upload,
  Image as ImageIcon
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { VendorShopService } from '@/lib/services/vendorShops'
import { ShopProductService } from '@/lib/services/shopProducts'
import { OrderService } from '@/lib/services/orders'
import { StorageService } from '@/lib/services/storage'
import { VendorShop, ShopProduct, ShopOrder } from '@/types'
import { formatCurrency } from '@/lib/utils'
import { showAlert, showConfirmation } from '@/components/ui/ConfirmationDialog'
import TrackingModal from '@/components/orders/TrackingModal'
import MerchantWallet from '@/components/dashboard/MerchantWallet'

interface ShopStats {
  totalProducts: number
  totalViews: number
  totalFollowers: number
  totalSales: number
}

export default function MyShopPage() {
  const { user, loading: authLoading } = useAuth()
  const [shops, setShops] = useState<VendorShop[]>([])
  const [selectedShop, setSelectedShop] = useState<VendorShop | null>(null)
  const [shopProducts, setShopProducts] = useState<ShopProduct[]>([])
  const [shopOrders, setShopOrders] = useState<ShopOrder[]>([])
  const [shopStats, setShopStats] = useState<ShopStats>({
    totalProducts: 0,
    totalViews: 0,
    totalFollowers: 0,
    totalSales: 0
  })
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'orders' | 'wallet'>('overview')
  const [selectedOrder, setSelectedOrder] = useState<ShopOrder | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [showTrackingModal, setShowTrackingModal] = useState(false)
  const [orderToShip, setOrderToShip] = useState<ShopOrder | null>(null)
  const [trackingLoading, setTrackingLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadingBanner, setUploadingBanner] = useState(false)
  const [uploadingLogo, setUploadingLogo] = useState(false)

  const fetchUserShops = async () => {
    if (!user?.id) {
      setError('User not authenticated')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const userShops = await VendorShopService.getUserShops(user.id)
      setShops(userShops)

      if (userShops.length > 0) {
        setSelectedShop(userShops[0])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load shops')
    } finally {
      setLoading(false)
    }
  }

  const fetchShopProducts = useCallback(async () => {
    if (!selectedShop?.id) {
      setShopProducts([])
      return
    }

    try {
      const { products } = await VendorShopService.getShopProducts(selectedShop.id, 1, 20)
      setShopProducts(products)
    } catch (err) {
      console.error('Error fetching shop products:', err)
      // Don't throw error, just log it and continue with empty products
      setShopProducts([])
    }
  }, [selectedShop?.id])

  const fetchShopOrders = useCallback(async () => {
    if (!selectedShop?.id || !user?.id) {
      setShopOrders([])
      return
    }

    try {
      const { orders } = await OrderService.getSellerOrders(user.id, selectedShop.id, 1, 20)
      setShopOrders(orders)
    } catch (err) {
      console.error('Error fetching shop orders:', err)
      // Don't throw error, just log it and continue with empty orders
      setShopOrders([])
    }
  }, [selectedShop?.id, user?.id])

  const fetchShopStats = useCallback(async () => {
    if (!selectedShop?.id) {
      setShopStats({
        totalProducts: 0,
        totalViews: 0,
        totalFollowers: 0,
        totalSales: 0
      })
      return
    }

    try {
      // Refresh shop statistics from database
      const refreshedShop = await VendorShopService.refreshShopStatistics(selectedShop.id)

      // Get followers count with error handling
      let followersCount = 0
      try {
        followersCount = await VendorShopService.getShopFollowersCount(selectedShop.id)
      } catch (followersError) {
        console.warn('Failed to get followers count, using 0:', followersError)
      }

      // Update only the statistics state
      setShopStats({
        totalProducts: refreshedShop.total_products || 0,
        totalViews: refreshedShop.total_views || 0,
        totalFollowers: followersCount,
        totalSales: refreshedShop.total_sales || 0
      })

    } catch (err) {
      console.error('Error fetching shop stats:', err)

      // Fallback to existing data if refresh fails
      let followersCount = 0
      try {
        followersCount = await VendorShopService.getShopFollowersCount(selectedShop.id)
      } catch (followersError) {
        console.warn('Failed to get followers count in fallback, using 0:', followersError)
      }

      setShopStats({
        totalProducts: selectedShop.total_products || 0,
        totalViews: selectedShop.total_views || 0,
        totalFollowers: followersCount,
        totalSales: selectedShop.total_sales || 0
      })
    }
  }, [selectedShop?.id]) // Only depend on shop ID, not the entire shop object

  // useEffect hooks - placed after all function declarations to avoid hoisting issues
  useEffect(() => {
    if (user) {
      fetchUserShops()
    }
  }, [user])

  useEffect(() => {
    if (selectedShop) {
      fetchShopProducts()
      fetchShopOrders()
      fetchShopStats()
    }
  }, [selectedShop?.id, fetchShopProducts, fetchShopOrders, fetchShopStats]) // Only trigger when shop ID changes

  const handleViewProduct = (productId: string) => {
    window.open(`/product/${productId}`, '_blank')
  }

  const handleEditProduct = (productId: string) => {
    // TODO: Create edit product page
    window.open(`/dashboard/shop/products/edit/${productId}`, '_blank')
  }

  const handleDeleteProduct = async (productId: string, productTitle: string) => {
    const confirmed = await showConfirmation({
      title: 'Delete Product',
      message: `Are you sure you want to delete "${productTitle}"? This action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete'
    })

    if (confirmed) {
      try {
        await ShopProductService.deleteProduct(productId)
        await showAlert({
          title: 'Success',
          message: 'Product deleted successfully!',
          variant: 'success'
        })
        // Refresh the products list
        fetchShopProducts()
      } catch (error) {
        await showAlert({
          title: 'Error',
          message: error instanceof Error ? error.message : 'Failed to delete product',
          variant: 'danger'
        })
      }
    }
  }

  const handleViewOrder = (order: ShopOrder) => {
    setSelectedOrder(order)
    setShowOrderDetails(true)
  }

  const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
    // If marking as shipped, show tracking modal
    if (newStatus === 'shipped') {
      const order = shopOrders.find(o => o.id === orderId)
      if (order) {
        setOrderToShip(order)
        setShowTrackingModal(true)
        return
      }
    }

    // Special handling for cancellation
    if (newStatus === 'cancelled') {
      const confirmed = await showConfirmation({
        title: 'Cancel Order',
        message: 'Are you sure you want to cancel this order? The payment will be refunded to the buyer\'s wallet.',
        confirmText: 'Cancel Order',
        cancelText: 'Keep Order',
        variant: 'danger'
      })

      if (!confirmed) return

      try {
        await OrderService.cancelOrderWithRefund(orderId, user?.id || '')
        await showAlert({
          title: 'Success',
          message: 'Order cancelled and refund processed successfully!',
          variant: 'success'
        })
        fetchShopOrders()
      } catch (error) {
        await showAlert({
          title: 'Error',
          message: error instanceof Error ? error.message : 'Failed to cancel order',
          variant: 'danger'
        })
      }
      return
    }

    const confirmed = await showConfirmation({
      title: 'Update Order Status',
      message: `Are you sure you want to update the order status to "${newStatus.replace('_', ' ')}"?`,
      confirmText: 'Update',
      cancelText: 'Cancel',
      variant: 'warning'
    })

    if (!confirmed) return

    try {
      await OrderService.updateOrderStatus(orderId, newStatus, undefined, undefined, undefined, user?.id)
      await showAlert({
        title: 'Success',
        message: 'Order status updated successfully!',
        variant: 'success'
      })
      fetchShopOrders()
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update order status',
        variant: 'danger'
      })
    }
  }

  const handleAddTracking = async (trackingData: {
    trackingNumber: string
    courierService: string
    trackingUrl?: string
  }) => {
    if (!orderToShip) return

    try {
      setTrackingLoading(true)

      await OrderService.updateOrderStatus(
        orderToShip.id,
        'shipped',
        trackingData.trackingNumber,
        trackingData.trackingUrl,
        `Shipped via ${trackingData.courierService}`,
        user?.id,
        trackingData.courierService
      )

      await showAlert({
        title: 'Success',
        message: 'Order marked as shipped with tracking information!',
        variant: 'success'
      })

      setShowTrackingModal(false)
      setOrderToShip(null)
      fetchShopOrders()
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to update order status',
        variant: 'danger'
      })
    } finally {
      setTrackingLoading(false)
    }
  }

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'pending_shipment':
        return 'text-orange-600 bg-orange-100'
      case 'confirmed':
        return 'text-blue-600 bg-blue-100'
      case 'processing':
        return 'text-purple-600 bg-purple-100'
      case 'shipped':
        return 'text-indigo-600 bg-indigo-100'
      case 'delivered':
        return 'text-green-600 bg-green-100'
      case 'cancelled':
        return 'text-red-600 bg-red-100'
      case 'refunded':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100'
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'rejected':
        return 'text-red-600 bg-red-100'
      case 'suspended':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'rejected':
        return <XCircle className="h-4 w-4" />
      case 'suspended':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const handleBannerUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !selectedShop || !user?.id) return

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      await showAlert({
        title: 'Invalid File',
        message: 'Please select an image file.',
        variant: 'danger'
      })
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      await showAlert({
        title: 'File Too Large',
        message: 'Image size must be less than 5MB.',
        variant: 'danger'
      })
      return
    }

    try {
      setUploadingBanner(true)
      const bannerUrl = await StorageService.uploadImage(file, user.id)

      await VendorShopService.updateShop(selectedShop.id, { banner_url: bannerUrl })

      // Update the selected shop with new banner
      setSelectedShop(prev => prev ? { ...prev, banner_url: bannerUrl } : null)

      // Update shops list
      setShops(prev => prev.map(shop =>
        shop.id === selectedShop.id ? { ...shop, banner_url: bannerUrl } : shop
      ))

      await showAlert({
        title: 'Success',
        message: 'Shop banner updated successfully!',
        variant: 'success'
      })
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to upload banner',
        variant: 'danger'
      })
    } finally {
      setUploadingBanner(false)
    }
  }

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !selectedShop || !user?.id) return

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      await showAlert({
        title: 'Invalid File',
        message: 'Please select an image file.',
        variant: 'danger'
      })
      return
    }

    if (file.size > 2 * 1024 * 1024) { // 2MB limit for logo
      await showAlert({
        title: 'File Too Large',
        message: 'Logo size must be less than 2MB.',
        variant: 'danger'
      })
      return
    }

    try {
      setUploadingLogo(true)
      const logoUrl = await StorageService.uploadImage(file, user.id)

      await VendorShopService.updateShop(selectedShop.id, { logo_url: logoUrl })

      // Update the selected shop with new logo
      setSelectedShop(prev => prev ? { ...prev, logo_url: logoUrl } : null)

      // Update shops list
      setShops(prev => prev.map(shop =>
        shop.id === selectedShop.id ? { ...shop, logo_url: logoUrl } : shop
      ))

      await showAlert({
        title: 'Success',
        message: 'Shop logo updated successfully!',
        variant: 'success'
      })
    } catch (error) {
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to upload logo',
        variant: 'danger'
      })
    } finally {
      setUploadingLogo(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
          <div className="text-red-600 mb-4">Please sign in to access your shop</div>
        </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error}</div>
          <button
            onClick={fetchUserShops}
            className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90"
          >
            Try Again
          </button>
        </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Shop</h1>
            <p className="text-gray-600">Manage your vendor shop and products</p>
            {selectedShop && (
              <div className="mt-2">
                <h2 className="text-lg font-semibold text-primary-blue">{selectedShop.name}</h2>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedShop.status)}`}>
                  {getStatusIcon(selectedShop.status)}
                  <span className="ml-1 capitalize">{selectedShop.status}</span>
                </span>
              </div>
            )}
          </div>
          {shops.length === 0 && (
            <div className="flex gap-3">
              <Link
                href="/create-shop"
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Shop
              </Link>
            </div>
          )}
        </div>

        {shops.length === 0 ? (
          /* No Shops State */
          <div className="text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200">
            <Store className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No shops yet</h3>
            <p className="text-gray-600 mb-6">Create your first shop to start selling products</p>
            <Link
              href="/create-shop"
              className="inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              Create Your First Shop
            </Link>
          </div>
        ) : (
          <div className="space-y-6">


            {selectedShop && (
              <>
                {/* Tab Navigation */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                  <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6" aria-label="Tabs">
                      <button
                        onClick={() => setActiveTab('overview')}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'overview'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        Overview
                      </button>
                      <button
                        onClick={() => setActiveTab('products')}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'products'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        Products ({shopProducts.length})
                      </button>
                      <button
                        onClick={() => setActiveTab('orders')}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'orders'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        Orders ({shopOrders.length})
                      </button>
                      <button
                        onClick={() => setActiveTab('wallet')}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'wallet'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        Merchant Wallet
                      </button>
                    </nav>
                  </div>
                </div>

                {/* Tab Content */}
                {activeTab === 'overview' && (
                  <>
                    {/* Shop Overview */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  {/* Shop Banner */}
                  <div className="relative h-48 bg-gradient-to-r from-primary-blue to-secondary-blue">
                    {selectedShop.banner_url ? (
                      <img
                        src={selectedShop.banner_url}
                        alt={`${selectedShop.name} banner`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-center text-white">
                          <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                          <p className="text-sm opacity-75">No banner image</p>
                        </div>
                      </div>
                    )}

                    {/* Banner Upload Button */}
                    <div className="absolute top-4 right-4">
                      <label className="cursor-pointer">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleBannerUpload}
                          className="hidden"
                          disabled={uploadingBanner}
                        />
                        <div className="flex items-center px-3 py-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors">
                          {uploadingBanner ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                          ) : (
                            <Camera className="h-4 w-4 mr-2" />
                          )}
                          <span className="text-sm">
                            {uploadingBanner ? 'Uploading...' : 'Change Banner'}
                          </span>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex items-start space-x-4">
                        {/* Shop Logo */}
                        <div className="relative">
                          <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-200 border-4 border-white shadow-lg">
                            {selectedShop.logo_url ? (
                              <img
                                src={selectedShop.logo_url}
                                alt={`${selectedShop.name} logo`}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Store className="h-8 w-8 text-gray-400" />
                              </div>
                            )}
                          </div>

                          {/* Logo Upload Button */}
                          <label className="absolute -bottom-2 -right-2 cursor-pointer">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleLogoUpload}
                              className="hidden"
                              disabled={uploadingLogo}
                            />
                            <div className="w-8 h-8 bg-primary-blue text-white rounded-full flex items-center justify-center hover:bg-primary-blue/90 transition-colors shadow-lg">
                              {uploadingLogo ? (
                                <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
                              ) : (
                                <Camera className="h-3 w-3" />
                              )}
                            </div>
                          </label>
                        </div>

                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">{selectedShop.name}</h2>
                          <p className="text-gray-600 mt-1">{selectedShop.description}</p>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="text-sm text-gray-500">Created</div>
                        <div className="font-medium text-gray-900">
                          {new Date(selectedShop.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>

                    {/* Shop Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                        <div className="flex items-center">
                          <Package className="h-8 w-8 text-blue-600" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-blue-700">Products</p>
                            <p className="text-2xl font-bold text-blue-900">{shopStats.totalProducts}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                        <div className="flex items-center">
                          <Eye className="h-8 w-8 text-green-600" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-green-700">Views</p>
                            <p className="text-2xl font-bold text-green-900">{shopStats.totalViews}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                        <div className="flex items-center">
                          <TrendingUp className="h-8 w-8 text-purple-600" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-purple-700">Sales</p>
                            <p className="text-2xl font-bold text-purple-900">{shopStats.totalSales}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4">
                        <div className="flex items-center">
                          <Star className="h-8 w-8 text-yellow-600" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-yellow-700">Rating</p>
                            <p className="text-2xl font-bold text-yellow-900">{selectedShop.rating?.toFixed(1) || '0.0'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                  </>
                )}

                {/* Products Tab */}
                {activeTab === 'products' && (
                  <>
                    {/* Products Section */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">Products</h3>
                        <p className="text-gray-600 mt-1">Manage your shop products</p>
                      </div>
                      <Link
                        href="/dashboard/shop/products/add"
                        className="flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Product
                      </Link>
                    </div>
                  </div>

                  {shopProducts.length === 0 ? (
                    <div className="text-center py-12">
                      <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h4 className="text-xl font-semibold text-gray-900 mb-2">No products yet</h4>
                      <p className="text-gray-600 mb-6">Start adding products to your shop to begin selling</p>
                      <Link
                        href="/dashboard/shop/products/add"
                        className="inline-flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Add Your First Product
                      </Link>
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-200">
                      {shopProducts.map((product) => (
                        <div key={product.id} className="p-6 hover:bg-gray-50 transition-colors">
                          <div className="flex items-start space-x-4">
                            {/* Product Image */}
                            <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                              {product.images && product.images.length > 0 ? (
                                <img
                                  src={product.images[0].image_url}
                                  alt={product.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Package className="h-8 w-8 text-gray-400" />
                                </div>
                              )}
                            </div>

                            {/* Product Details */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                    {product.title}
                                  </h4>
                                  <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                                    {product.description}
                                  </p>
                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>SKU: {product.sku || 'N/A'}</span>
                                    <span>•</span>
                                    <span className="capitalize">{product.condition}</span>
                                    <span>•</span>
                                    <span>Stock: {product.stock_quantity}</span>
                                    <span>•</span>
                                    <span>Views: {product.views || 0}</span>
                                  </div>
                                </div>
                                <div className="text-right ml-4">
                                  <div className="text-2xl font-bold text-primary-blue mb-1">
                                    {formatCurrency(product.price || 0)}
                                  </div>
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    product.status === 'active'
                                      ? 'bg-green-100 text-green-800'
                                      : product.status === 'inactive'
                                      ? 'bg-gray-100 text-gray-800'
                                      : product.status === 'out_of_stock'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {product.status.replace('_', ' ')}
                                  </span>
                                </div>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex items-center space-x-2 mt-4">
                                <button
                                  onClick={() => handleViewProduct(product.id)}
                                  className="flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                  title="View Product"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </button>
                                <button
                                  onClick={() => handleEditProduct(product.id)}
                                  className="flex items-center px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                                  title="Edit Product"
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  Edit
                                </button>
                                <button
                                  onClick={() => handleDeleteProduct(product.id, product.title)}
                                  className="flex items-center px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                                  title="Delete Product"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Delete
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                  </>
                )}

                {/* Orders Tab */}
                {activeTab === 'orders' && (
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">Orders</h3>
                          <p className="text-gray-600 mt-1">Manage your shop orders</p>
                        </div>
                      </div>
                    </div>

                    {shopOrders.length === 0 ? (
                      <div className="text-center py-12">
                        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">No orders yet</h4>
                        <p className="text-gray-600">Orders will appear here when customers purchase your products</p>
                      </div>
                    ) : (
                      <div className="divide-y divide-gray-200">
                        {shopOrders.map((order) => (
                          <div key={order.id} className="p-6 hover:bg-gray-50 transition-colors">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <h4 className="text-lg font-semibold text-gray-900">
                                  Order #{order.order_number}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {order.buyer?.full_name} • {new Date(order.created_at).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="text-lg font-bold text-gray-900">
                                  {formatCurrency(order.total_amount)}
                                </p>
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getOrderStatusColor(order.status)}`}>
                                  {order.status.replace('_', ' ').charAt(0).toUpperCase() + order.status.replace('_', ' ').slice(1)}
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                <span>{order.order_items?.length || 0} item(s)</span>
                                <span>•</span>
                                <span>{order.payment_method.replace('_', ' ')}</span>
                                {order.tracking_number && (
                                  <>
                                    <span>•</span>
                                    <span>Tracking: {order.tracking_number}</span>
                                  </>
                                )}
                              </div>
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => handleViewOrder(order)}
                                  className="flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </button>
                                {order.status !== 'delivered' && order.status !== 'cancelled' && order.status !== 'refunded' && (
                                  <select
                                    value={order.status}
                                    onChange={(e) => handleUpdateOrderStatus(order.id, e.target.value)}
                                    className="text-sm border border-gray-300 rounded-lg px-2 py-1.5 focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                                  >
                                    {order.status === 'pending_shipment' && (
                                      <>
                                        <option value="pending_shipment">Pending Shipment</option>
                                        <option value="shipped">Ship Order</option>
                                        <option value="cancelled">Cancel Order</option>
                                      </>
                                    )}
                                    {order.status === 'shipped' && (
                                      <option value="shipped">Shipped</option>
                                    )}
                                  </select>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Merchant Wallet Tab */}
                {activeTab === 'wallet' && selectedShop && user?.id && (
                  <MerchantWallet
                    shopId={selectedShop.id}
                    userId={user.id}
                  />
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Order #{selectedOrder.order_number}
                </h2>
                <p className="text-gray-600">
                  {selectedOrder.buyer?.full_name} • {new Date(selectedOrder.created_at).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={() => setShowOrderDetails(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Order Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Order Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOrderStatusColor(selectedOrder.status)}`}>
                        {selectedOrder.status.replace('_', ' ').charAt(0).toUpperCase() + selectedOrder.status.replace('_', ' ').slice(1)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment:</span>
                      <span>{selectedOrder.payment_method.replace('_', ' ')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total:</span>
                      <span className="font-semibold">{formatCurrency(selectedOrder.total_amount)}</span>
                    </div>
                  </div>
                </div>

                {/* Customer Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-600">Name:</span>
                      <span className="ml-2">{selectedOrder.shipping_address.name}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Phone:</span>
                      <span className="ml-2">{selectedOrder.shipping_address.phone}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Address:</span>
                      <div className="ml-2 mt-1">
                        <p>{selectedOrder.shipping_address.address}</p>
                        <p>{selectedOrder.shipping_address.city}, {selectedOrder.shipping_address.district}</p>
                        {selectedOrder.shipping_address.postal_code && (
                          <p>{selectedOrder.shipping_address.postal_code}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div className="space-y-3">
                  {selectedOrder.order_items?.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0"></div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.product_title}</h4>
                        <p className="text-sm text-gray-600">
                          Qty: {item.quantity} × {formatCurrency(item.unit_price)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(item.total_price)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Notes */}
              {selectedOrder.buyer_notes && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Customer Notes</h3>
                  <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                    {selectedOrder.buyer_notes}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Tracking Modal */}
      {showTrackingModal && orderToShip && (
        <TrackingModal
          isOpen={showTrackingModal}
          onClose={() => {
            setShowTrackingModal(false)
            setOrderToShip(null)
          }}
          onSubmit={handleAddTracking}
          loading={trackingLoading}
        />
      )}
    </DashboardLayout>
  )
}
