"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/extended-commission-structure/route";
exports.ids = ["app/api/admin/extended-commission-structure/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_extended_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/extended-commission-structure/route.ts */ \"(rsc)/./src/app/api/admin/extended-commission-structure/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/extended-commission-structure/route\",\n        pathname: \"/api/admin/extended-commission-structure\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/extended-commission-structure/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\extended-commission-structure\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_extended_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/extended-commission-structure/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/extended-commission-structure/route.ts":
/*!******************************************************************!*\
  !*** ./src/app/api/admin/extended-commission-structure/route.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        // For testing purposes, temporarily skip authentication\n        // TODO: Add proper authentication back in production\n        // Get all commission structures\n        const { data: structures, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").select(\"*\").order(\"package_value\", {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch commission structures: ${error.message}`);\n        }\n        // Transform the data to match the expected extended schema\n        const transformedStructures = (structures || []).map((structure)=>({\n                id: structure.id,\n                commission_type: structure.commission_type,\n                package_value: structure.package_value,\n                // Map the old level rates to the new extended schema based on commission type\n                direct_commission_rate: structure.commission_type === \"direct_commission\" ? structure.level_1_rate : structure.direct_commission_rate || 0,\n                level_commission_rate: structure.commission_type === \"level_commission\" ? structure.level_1_rate : structure.level_commission_rate || 0,\n                voucher_rate: structure.commission_type === \"voucher\" ? structure.level_1_rate : structure.voucher_rate || 0,\n                festival_bonus_rate: structure.commission_type === \"festival_bonus\" ? structure.level_1_rate : structure.festival_bonus_rate || 0,\n                saving_rate: structure.commission_type === \"saving\" ? structure.level_1_rate : structure.saving_rate || 0,\n                gift_center_rate: structure.commission_type === \"gift_center\" ? structure.level_1_rate : structure.gift_center_rate || 0,\n                entertainment_rate: structure.commission_type === \"entertainment\" ? structure.level_1_rate : structure.entertainment_rate || 0,\n                medical_rate: structure.commission_type === \"medical\" ? structure.level_1_rate : structure.medical_rate || 0,\n                education_rate: structure.commission_type === \"education\" ? structure.level_1_rate : structure.education_rate || 0,\n                credit_rate: structure.commission_type === \"credit\" ? structure.level_1_rate : structure.credit_rate || 0,\n                zm_bonus_rate: structure.commission_type === \"zm_bonus\" ? structure.level_1_rate : structure.zm_bonus_rate || 0,\n                zm_petral_allowance_rate: structure.commission_type === \"zm_petral_allowance\" ? structure.level_1_rate : structure.zm_petral_allowance_rate || 0,\n                zm_leasing_facility_rate: structure.commission_type === \"zm_leasing_facility\" ? structure.level_1_rate : structure.zm_leasing_facility_rate || 0,\n                zm_phone_bill_rate: structure.commission_type === \"zm_phone_bill\" ? structure.level_1_rate : structure.zm_phone_bill_rate || 0,\n                rsm_bonus_rate: structure.commission_type === \"rsm_bonus\" ? structure.level_1_rate : structure.rsm_bonus_rate || 0,\n                rsm_petral_allowance_rate: structure.commission_type === \"rsm_petral_allowance\" ? structure.level_1_rate : structure.rsm_petral_allowance_rate || 0,\n                rsm_leasing_facility_rate: structure.commission_type === \"rsm_leasing_facility\" ? structure.level_1_rate : structure.rsm_leasing_facility_rate || 0,\n                rsm_phone_bill_rate: structure.commission_type === \"rsm_phone_bill\" ? structure.level_1_rate : structure.rsm_phone_bill_rate || 0,\n                zm_present_leader_rate: structure.commission_type === \"zm_present_leader\" ? structure.level_1_rate : structure.zm_present_leader_rate || 0,\n                rsm_present_leader_rate: structure.commission_type === \"rsm_present_leader\" ? structure.level_1_rate : structure.rsm_present_leader_rate || 0,\n                annual_present_leader_rate: structure.commission_type === \"annual_present_leader\" ? structure.level_1_rate : structure.annual_present_leader_rate || 0,\n                okdoi_head_rate: structure.commission_type === \"okdoi_head\" ? structure.level_1_rate : structure.okdoi_head_rate || 0,\n                is_active: structure.is_active,\n                created_at: structure.created_at,\n                updated_at: structure.updated_at\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedStructures\n        });\n    } catch (error) {\n        console.error(\"Error fetching commission structures:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // For testing purposes, temporarily skip authentication\n        // TODO: Add proper authentication back in production\n        const body = await request.json();\n        // Validate required fields\n        if (!body.commission_type || !body.package_value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Commission type and package value are required\"\n            }, {\n                status: 400\n            });\n        }\n        // For now, we'll work with the existing schema and use level_1_rate for the primary rate\n        // The rate will be determined by the commission_type\n        let primaryRate = 0;\n        // Determine the primary rate based on commission type\n        switch(body.commission_type){\n            case \"direct_commission\":\n                primaryRate = body.direct_commission_rate || 0;\n                break;\n            case \"level_commission\":\n                primaryRate = body.level_commission_rate || 0;\n                break;\n            case \"voucher\":\n                primaryRate = body.voucher_rate || 0;\n                break;\n            case \"festival_bonus\":\n                primaryRate = body.festival_bonus_rate || 0;\n                break;\n            case \"saving\":\n                primaryRate = body.saving_rate || 0;\n                break;\n            case \"gift_center\":\n                primaryRate = body.gift_center_rate || 0;\n                break;\n            case \"entertainment\":\n                primaryRate = body.entertainment_rate || 0;\n                break;\n            case \"medical\":\n                primaryRate = body.medical_rate || 0;\n                break;\n            case \"education\":\n                primaryRate = body.education_rate || 0;\n                break;\n            case \"credit\":\n                primaryRate = body.credit_rate || 0;\n                break;\n            case \"zm_bonus\":\n                primaryRate = body.zm_bonus_rate || 0;\n                break;\n            case \"zm_petral_allowance\":\n                primaryRate = body.zm_petral_allowance_rate || 0;\n                break;\n            case \"zm_leasing_facility\":\n                primaryRate = body.zm_leasing_facility_rate || 0;\n                break;\n            case \"zm_phone_bill\":\n                primaryRate = body.zm_phone_bill_rate || 0;\n                break;\n            case \"rsm_bonus\":\n                primaryRate = body.rsm_bonus_rate || 0;\n                break;\n            case \"rsm_petral_allowance\":\n                primaryRate = body.rsm_petral_allowance_rate || 0;\n                break;\n            case \"rsm_leasing_facility\":\n                primaryRate = body.rsm_leasing_facility_rate || 0;\n                break;\n            case \"rsm_phone_bill\":\n                primaryRate = body.rsm_phone_bill_rate || 0;\n                break;\n            case \"zm_present_leader\":\n                primaryRate = body.zm_present_leader_rate || 0;\n                break;\n            case \"rsm_present_leader\":\n                primaryRate = body.rsm_present_leader_rate || 0;\n                break;\n            case \"annual_present_leader\":\n                primaryRate = body.annual_present_leader_rate || 0;\n                break;\n            case \"okdoi_head\":\n                primaryRate = body.okdoi_head_rate || 0;\n                break;\n            default:\n                primaryRate = 0;\n        }\n        // Create new commission structure using the existing schema\n        const { data: newStructure, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").insert({\n            commission_type: body.commission_type,\n            package_value: body.package_value,\n            level_1_rate: primaryRate,\n            level_2_rate: 0.02,\n            level_3_rate: 0.02,\n            level_4_rate: 0.02,\n            level_5_rate: 0.02,\n            level_6_rate: 0.02,\n            level_7_rate: 0.02,\n            level_8_rate: 0.02,\n            level_9_rate: 0.02,\n            level_10_rate: 0.02,\n            is_active: body.is_active !== undefined ? body.is_active : true\n        }).select().single();\n        if (error) {\n            throw new Error(`Failed to create commission structure: ${error.message}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Commission structure created successfully\",\n            data: newStructure\n        });\n    } catch (error) {\n        console.error(\"Error creating commission structure:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/extended-commission-structure/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fextended-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();